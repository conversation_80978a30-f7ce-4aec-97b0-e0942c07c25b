@echo off
REM 使用BarTokenizer高质量tokens训练BarGpt4模型
REM 测试是否能够解决样本不均衡造成的预测集中问题

e:
cd e:\lab\RoboQuant\pylab

@REM "=== 使用BarTokenizer训练BarGpt4模型 ==="
@REM "目标：解决样本不均衡造成的预测集中问题"
@REM "数据：min1周期数据"
@REM ""

REM 基础配置 - 使用quantile映射策略
python train_bar_gpt4_with_tokenizer.py ^
--data_file f:/hqdata/fut_top_min1.parquet ^
--data_path f:/hqdata ^
--block_size 30 ^
--mapping_strategy quantile ^
--balancing_strategy frequency ^
--n_bins 100 ^
--max_token_frequency 0.08 ^
--gini_threshold 0.6 ^
--n_layer 4 ^
--n_head 8 ^
--d_model 128 ^
--time_encoding timeF ^
--time_embed_type time_feature ^
--pos_embed_type rope ^
--dropout 0.1 ^
--batch_size 64 ^
--lr 1e-4 ^
--weight_decay 0.01 ^
--max_epochs 3 ^
--k_folds 3 ^
--early_stop 5 ^
--min_delta 1e-3 ^
--num_workers 0 ^
--seed 42 ^
--log_dir lightning_logs_tokenized ^
--use_class_weights

@REM echo ""
@REM echo "=== 第一轮训练完成 ==="
@REM echo ""

REM 对比实验 - 使用adaptive映射策略
@REM echo "=== 开始对比实验：adaptive映射策略 ==="

python train_bar_gpt4_with_tokenizer.py ^
--data_file f:/hqdata/fut_top_min1.parquet ^
--data_path f:/hqdata ^
--block_size 30 ^
--mapping_strategy adaptive ^
--balancing_strategy frequency ^
--n_bins 100 ^
--max_token_frequency 0.08 ^
--gini_threshold 0.6 ^
--n_layer 4 ^
--n_head 8 ^
--d_model 128 ^
--time_encoding timeF ^
--time_embed_type time_feature ^
--pos_embed_type rope ^
--dropout 0.1 ^
--batch_size 64 ^
--lr 1e-4 ^
--weight_decay 0.01 ^
--max_epochs 3 ^
--k_folds 3 ^
--early_stop 5 ^
--min_delta 1e-3 ^
--num_workers 0 ^
--seed 42 ^
--log_dir lightning_logs_tokenized_adaptive ^
--use_class_weights

@REM echo ""
@REM echo "=== 第二轮训练完成 ==="
@REM echo ""

REM 对比实验 - 不使用平衡策略
@REM echo "=== 开始对比实验：不使用平衡策略 ==="

python train_bar_gpt4_with_tokenizer.py ^
--data_file f:/hqdata/fut_top_min1.parquet ^
--data_path f:/hqdata ^
--block_size 30 ^
--mapping_strategy quantile ^
--balancing_strategy none ^
--n_bins 100 ^
--max_token_frequency 0.2 ^
--gini_threshold 0.9 ^
--n_layer 4 ^
--n_head 8 ^
--d_model 128 ^
--time_encoding timeF ^
--time_embed_type time_feature ^
--pos_embed_type rope ^
--dropout 0.1 ^
--batch_size 64 ^
--lr 1e-4 ^
--weight_decay 0.01 ^
--max_epochs 3 ^
--k_folds 3 ^
--early_stop 5 ^
--min_delta 1e-3 ^
--num_workers 0 ^
--seed 42 ^
--log_dir lightning_logs_tokenized_no_balance

@REM echo ""
@REM echo "=== 所有训练完成 ==="
@REM echo ""
@REM echo "请查看以下日志目录的结果："
@REM echo "1. lightning_logs_tokenized - quantile + frequency平衡"
@REM echo "2. lightning_logs_tokenized_adaptive - adaptive + frequency平衡"  
@REM echo "3. lightning_logs_tokenized_no_balance - quantile + 无平衡"
@REM echo ""
@REM echo "重点关注预测多样性和最高频预测占比指标"

@REM pause
