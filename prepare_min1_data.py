"""
准备min1数据用于BarTokenizer训练

从原始数据源提取min1周期的K线数据，并保存为parquet格式
供BarTokenizer训练使用。
"""

import os
import sys
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pyqlab.data.tokenizers.bar_tokenizer import BarTokenizer


def load_raw_data(data_path: str, market: str = 'fut', block_name: str = 'top', period: str = 'min1'):
    """
    加载原始数据
    
    Args:
        data_path: 数据路径
        market: 市场类型
        block_name: 数据块名称
        period: 周期
    
    Returns:
        DataFrame: 原始数据
    """
    print(f"正在加载原始数据...")
    print(f"  数据路径: {data_path}")
    print(f"  市场: {market}")
    print(f"  数据块: {block_name}")
    print(f"  周期: {period}")
    
    # 构建数据文件路径
    data_file = os.path.join(data_path, f"{market}_{block_name}_{period}.parquet")
    
    if not os.path.exists(data_file):
        print(f"❌ 数据文件不存在: {data_file}")
        
        # 尝试其他可能的文件名
        alternative_files = [
            f"{market}_{block_name}_{period}.csv",
            f"{market}_{period}.parquet",
            f"{block_name}_{period}.parquet"
        ]
        
        for alt_file in alternative_files:
            alt_path = os.path.join(data_path, alt_file)
            if os.path.exists(alt_path):
                print(f"✅ 找到替代文件: {alt_path}")
                data_file = alt_path
                break
        else:
            raise FileNotFoundError(f"未找到数据文件，请检查路径: {data_path}")
    
    # 读取数据
    if data_file.endswith('.parquet'):
        df = pd.read_parquet(data_file)
    elif data_file.endswith('.csv'):
        df = pd.read_csv(data_file)
    else:
        raise ValueError(f"不支持的文件格式: {data_file}")
    
    print(f"✅ 数据加载完成，共 {len(df)} 条记录")
    print(f"  列名: {list(df.columns)}")
    
    return df


def clean_and_validate_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    清理和验证数据
    
    Args:
        df: 原始数据
    
    Returns:
        DataFrame: 清理后的数据
    """
    print("正在清理和验证数据...")
    
    original_len = len(df)
    
    # 检查必要的列
    required_cols = ['datetime', 'code', 'open', 'high', 'low', 'close', 'volume']
    missing_cols = [col for col in required_cols if col not in df.columns]
    
    if missing_cols:
        print(f"❌ 缺少必要的列: {missing_cols}")
        print(f"  可用列: {list(df.columns)}")
        
        # 尝试映射列名
        column_mapping = {
            'ts_code': 'code',
            'trade_date': 'datetime',
            'vol': 'volume',
            'amount': 'turnover'
        }
        
        for old_col, new_col in column_mapping.items():
            if old_col in df.columns and new_col in required_cols:
                df = df.rename(columns={old_col: new_col})
                print(f"  映射列名: {old_col} -> {new_col}")
        
        # 再次检查
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise ValueError(f"仍然缺少必要的列: {missing_cols}")
    
    # 转换数据类型
    df['datetime'] = pd.to_datetime(df['datetime'])
    
    numeric_cols = ['open', 'high', 'low', 'close', 'volume']
    for col in numeric_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 删除包含NaN的行
    df = df.dropna(subset=required_cols)
    
    # 验证OHLC数据的合理性
    invalid_ohlc = (
        (df['high'] < df['low']) |
        (df['high'] < df['open']) |
        (df['high'] < df['close']) |
        (df['low'] > df['open']) |
        (df['low'] > df['close']) |
        (df['volume'] < 0)
    )
    
    if invalid_ohlc.any():
        print(f"⚠️  发现 {invalid_ohlc.sum()} 条无效的OHLC数据，将被删除")
        df = df[~invalid_ohlc]
    
    # 按代码和时间排序
    df = df.sort_values(['code', 'datetime']).reset_index(drop=True)
    
    # 删除重复数据
    duplicates = df.duplicated(subset=['code', 'datetime'])
    if duplicates.any():
        print(f"⚠️  发现 {duplicates.sum()} 条重复数据，将被删除")
        df = df[~duplicates].reset_index(drop=True)
    
    print(f"✅ 数据清理完成:")
    print(f"  原始记录数: {original_len}")
    print(f"  清理后记录数: {len(df)}")
    print(f"  保留比例: {len(df) / original_len:.2%}")
    
    return df


def filter_data_by_time(df: pd.DataFrame, start_date: str = None, end_date: str = None) -> pd.DataFrame:
    """
    按时间过滤数据
    
    Args:
        df: 数据
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
    
    Returns:
        DataFrame: 过滤后的数据
    """
    if start_date is None and end_date is None:
        return df
    
    print(f"正在按时间过滤数据...")
    original_len = len(df)
    
    if start_date:
        start_dt = pd.to_datetime(start_date)
        df = df[df['datetime'] >= start_dt]
        print(f"  开始日期: {start_date}")
    
    if end_date:
        end_dt = pd.to_datetime(end_date)
        df = df[df['datetime'] <= end_dt]
        print(f"  结束日期: {end_date}")
    
    print(f"✅ 时间过滤完成:")
    print(f"  原始记录数: {original_len}")
    print(f"  过滤后记录数: {len(df)}")
    print(f"  保留比例: {len(df) / original_len:.2%}")
    
    return df


def analyze_data_quality(df: pd.DataFrame):
    """分析数据质量"""
    print("\n=== 数据质量分析 ===")
    
    # 基本统计
    print(f"总记录数: {len(df):,}")
    print(f"时间范围: {df['datetime'].min()} 到 {df['datetime'].max()}")
    print(f"证券数量: {df['code'].nunique()}")
    
    # 各证券的数据量
    code_counts = df['code'].value_counts()
    print(f"\n各证券数据量统计:")
    print(f"  平均: {code_counts.mean():.0f}")
    print(f"  中位数: {code_counts.median():.0f}")
    print(f"  最小: {code_counts.min()}")
    print(f"  最大: {code_counts.max()}")
    
    # 数据完整性
    missing_data = df.isnull().sum()
    if missing_data.any():
        print(f"\n缺失数据:")
        for col, count in missing_data.items():
            if count > 0:
                print(f"  {col}: {count} ({count/len(df):.2%})")
    else:
        print(f"\n✅ 无缺失数据")
    
    # 价格统计
    print(f"\n价格统计:")
    for col in ['open', 'high', 'low', 'close']:
        if col in df.columns:
            print(f"  {col}: {df[col].min():.2f} - {df[col].max():.2f}")
    
    # 成交量统计
    if 'volume' in df.columns:
        print(f"\n成交量统计:")
        print(f"  范围: {df['volume'].min():,} - {df['volume'].max():,}")
        print(f"  平均: {df['volume'].mean():,.0f}")


def test_tokenizer_on_sample(df: pd.DataFrame, sample_size: int = 1000):
    """在样本数据上测试BarTokenizer"""
    print(f"\n=== 测试BarTokenizer ===")
    
    # 随机采样
    if len(df) > sample_size:
        sample_df = df.sample(n=sample_size, random_state=42).sort_values(['code', 'datetime'])
        print(f"使用随机样本测试 (n={sample_size})")
    else:
        sample_df = df
        print(f"使用全部数据测试 (n={len(df)})")
    
    # 测试不同的映射策略
    strategies = ['linear', 'quantile', 'adaptive']
    
    for strategy in strategies:
        print(f"\n--- 测试 {strategy} 映射策略 ---")
        
        try:
            tokenizer = BarTokenizer(
                mapping_strategy=strategy,
                balancing_strategy='frequency',
                n_bins=50,
                features=['change', 'body', 'upper_shadow', 'lower_shadow', 'volume_ratio']
            )
            
            # 按证券分组测试
            test_code = sample_df['code'].iloc[0]
            code_data = sample_df[sample_df['code'] == test_code].head(100)
            
            if len(code_data) < 20:
                print(f"  跳过：数据量不足 ({len(code_data)} < 20)")
                continue
            
            # 拟合和转换
            tokens = tokenizer.fit_transform(code_data)
            
            # 分析结果
            balance_metrics = tokenizer.analyze_balance(tokens)
            
            print(f"  ✅ 成功生成 {len(tokens)} 个tokens")
            print(f"  词汇表大小: {tokenizer.get_vocab_size()}")
            print(f"  基尼系数: {balance_metrics['gini_coefficient']:.4f}")
            print(f"  标准化熵: {balance_metrics['normalized_entropy']:.4f}")
            
        except Exception as e:
            print(f"  ❌ 测试失败: {str(e)}")


def main():
    """主函数"""
    print("=== 准备min1数据用于BarTokenizer训练 ===\n")
    
    # 配置参数
    data_path = "f:/hqdata"  # 原始数据路径
    output_path = "f:/hqdata/fut_top_min1.parquet"  # 输出文件路径
    
    # 时间过滤（可选）
    start_date = "2025-01-01"  # 开始日期
    end_date = None  # 结束日期
    
    try:
        # 1. 加载原始数据
        df = load_raw_data(data_path, market='fut', block_name='top', period='min1')
        
        # 2. 清理和验证数据
        df = clean_and_validate_data(df)
        
        # 3. 按时间过滤
        if start_date or end_date:
            df = filter_data_by_time(df, start_date, end_date)
        
        # 4. 分析数据质量
        analyze_data_quality(df)
        
        # 5. 测试BarTokenizer
        test_tokenizer_on_sample(df)
        
        # 6. 保存处理后的数据
        print(f"\n正在保存数据到: {output_path}")
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 保存为parquet格式
        df.to_parquet(output_path, index=False)
        
        print(f"✅ 数据保存完成!")
        print(f"  文件大小: {os.path.getsize(output_path) / (1024*1024):.1f} MB")
        print(f"  记录数: {len(df):,}")
        
        print(f"\n现在可以使用以下命令开始训练:")
        print(f"python train_bar_gpt4_with_tokenizer.py --data_file {output_path}")
        
    except Exception as e:
        print(f"❌ 处理失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
