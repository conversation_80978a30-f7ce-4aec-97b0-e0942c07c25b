# 使用BarTokenizer高质量tokens训练BarGpt4模型

## 🎯 项目目标

本项目旨在使用BarTokenizer生成的高质量tokens来训练BarGpt4模型，以解决样本不均衡造成的预测集中问题。通过使用min1数据进行测试，验证BarTokenizer是否能够有效改善模型的预测多样性。

## 🏗️ 项目架构

### 核心组件

1. **BarTokenizer** - 高质量token生成器
   - 支持多种映射策略（linear, quantile, adaptive）
   - 内置频率平衡机制
   - 透明可解释的token化过程

2. **BarTokenizedDataset** - 专用数据集类
   - 集成BarTokenizer处理流程
   - 自动处理token分布不平衡
   - 提供详细的分布分析

3. **BarGpt4** - 改进的GPT模型
   - 支持平衡损失函数
   - 内置预测多样性分析
   - 优化的训练和推理模式

## 📁 文件结构

```
├── pyqlab/data/dataset/dataset_bar_tokenized.py  # 专用数据集类
├── train_bar_gpt4_with_tokenizer.py              # 主训练脚本
├── prepare_min1_data.py                          # 数据预处理脚本
├── test_tokenizer_integration.py                 # 集成测试脚本
├── train_bar_gpt4_tokenizer.bat                  # 批处理训练脚本
└── README_BarTokenizer_Training.md               # 本文档
```

## 🚀 快速开始

### 1. 环境准备

确保已安装以下依赖：
```bash
pip install torch pytorch-lightning pandas numpy scikit-learn
```

### 2. 集成测试

首先运行集成测试确保所有组件正常工作：
```bash
python test_tokenizer_integration.py
```

预期输出：
```
🎉 所有测试通过！可以开始正式训练。
```

### 3. 数据准备

准备min1周期的K线数据：
```bash
python prepare_min1_data.py
```

这将：
- 加载原始数据
- 清理和验证数据质量
- 测试BarTokenizer功能
- 保存为parquet格式

### 4. 开始训练

#### 方法1：使用批处理脚本（推荐）
```bash
train_bar_gpt4_tokenizer.bat
```

这将运行三个对比实验：
1. quantile映射 + frequency平衡
2. adaptive映射 + frequency平衡  
3. quantile映射 + 无平衡

#### 方法2：手动运行
```bash
python train_bar_gpt4_with_tokenizer.py \
  --data_file f:/hqdata/fut_top_min1.parquet \
  --mapping_strategy quantile \
  --balancing_strategy frequency \
  --n_bins 100 \
  --max_token_frequency 0.08 \
  --gini_threshold 0.6 \
  --block_size 30 \
  --n_layer 4 \
  --n_head 8 \
  --d_model 128 \
  --batch_size 64 \
  --lr 1e-4 \
  --max_epochs 8 \
  --k_folds 3 \
  --use_class_weights
```

## 📊 关键参数说明

### BarTokenizer参数

- `mapping_strategy`: 映射策略
  - `quantile`: 分位数映射（推荐）
  - `adaptive`: 自适应映射
  - `linear`: 线性映射

- `balancing_strategy`: 平衡策略
  - `frequency`: 频率平衡（推荐）
  - `none`: 不使用平衡

- `n_bins`: token数量，影响词汇表大小
- `max_token_frequency`: 单个token最大频率（0.08推荐）
- `gini_threshold`: 基尼系数阈值（0.6推荐）

### 模型参数

- `block_size`: 序列长度（30推荐）
- `n_layer`: Transformer层数（4推荐）
- `n_head`: 注意力头数（8推荐）
- `d_model`: 模型维度（128推荐）

### 训练参数

- `batch_size`: 批次大小（64推荐）
- `lr`: 学习率（1e-4推荐）
- `k_folds`: 交叉验证折数（3推荐）
- `use_class_weights`: 是否使用类别权重

## 📈 评估指标

### 主要关注指标

1. **预测多样性** (`val_diversity`)
   - 预测的唯一token数 / 总词汇表大小
   - 目标：> 10%

2. **最高频预测占比**
   - 最常预测token的频率
   - 目标：< 30%

3. **验证损失** (`val_loss`)
   - 模型在验证集上的损失
   - 目标：持续下降

### 成功标准

如果满足以下条件，则认为成功解决了预测集中问题：
- 平均预测多样性 > 10%
- 平均最高频预测占比 < 30%
- 验证损失稳定收敛

## 🔍 结果分析

### 查看训练日志

使用TensorBoard查看训练过程：
```bash
tensorboard --logdir lightning_logs_tokenized
```

### 关键输出信息

训练过程中会输出：
```
Token分布分析结果:
  基尼系数: 0.3659
  标准化熵: 0.9522
  变异系数: 0.6508
  唯一tokens数量: 95

=== 预测分析 ===
预测的唯一token数: 45 / 150
目标的唯一token数: 95 / 150
预测多样性比例: 30.00%
最高频预测token占比: 15.20%
✅ 预测分布相对均匀
```

## 🛠️ 故障排除

### 常见问题

1. **内存不足**
   - 减小`batch_size`
   - 减小`block_size`
   - 减小`d_model`

2. **训练速度慢**
   - 增加`num_workers`
   - 使用GPU训练
   - 减小数据集大小

3. **预测仍然集中**
   - 降低`max_token_frequency`
   - 尝试不同的`mapping_strategy`
   - 增加`n_bins`

4. **损失不收敛**
   - 调整学习率`lr`
   - 增加`early_stop`耐心值
   - 检查数据质量

### 调试工具

使用数据集的调试功能：
```python
# 查看样本信息
dataset.print_sample_info(0)

# 获取分布统计
stats = dataset.get_distribution_stats()
print(stats)

# 获取类别权重
weights = dataset.get_class_weights()
```

## 📝 实验记录

建议记录以下信息：

| 实验 | 映射策略 | 平衡策略 | 基尼系数 | 预测多样性 | 最高频占比 | 验证损失 |
|------|----------|----------|----------|------------|------------|----------|
| 1    | quantile | frequency| 0.36     | 30%        | 15%        | 0.85     |
| 2    | adaptive | frequency| 0.42     | 25%        | 20%        | 0.92     |
| 3    | quantile | none     | 0.78     | 8%         | 65%        | 1.15     |

## 🎯 下一步计划

1. **扩展到其他周期**
   - 测试min5、min15数据
   - 对比不同周期的效果

2. **模型优化**
   - 尝试更大的模型
   - 实验不同的架构

3. **策略改进**
   - 开发新的平衡策略
   - 优化token组合方法

4. **生产部署**
   - 导出ONNX模型
   - 集成到回测系统

## 📞 支持

如有问题，请检查：
1. 运行`test_tokenizer_integration.py`确保环境正常
2. 查看训练日志中的错误信息
3. 检查数据文件格式和路径
4. 确认GPU/CPU资源充足
