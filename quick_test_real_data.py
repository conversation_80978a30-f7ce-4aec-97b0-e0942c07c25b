"""
快速测试真实数据的BarTokenizer训练

这个脚本用于快速验证真实数据是否能够正常工作，
运行一个小规模的训练测试。
"""

import os
import sys
import torch
import pandas as pd
import numpy as np
from argparse import ArgumentParser

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pyqlab.data.dataset.dataset_bar_tokenized import BarTokenizedDataset
from pyqlab.models.gpt.bar_gpt4 import BarGpt4


def quick_data_check(data_file: str):
    """快速检查数据文件"""
    print(f"=== 检查数据文件: {data_file} ===")
    
    if not os.path.exists(data_file):
        print(f"❌ 数据文件不存在: {data_file}")
        return False
    
    try:
        # 读取数据
        df = pd.read_parquet(data_file)
        print(f"✅ 数据加载成功")
        print(f"  记录数: {len(df):,}")
        print(f"  列名: {list(df.columns)}")
        print(f"  时间范围: {df['datetime'].min()} 到 {df['datetime'].max()}")
        print(f"  证券数量: {df['code'].nunique()}")
        
        # 检查必要的列
        required_cols = ['datetime', 'code', 'open', 'high', 'low', 'close', 'volume']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            print(f"❌ 缺少必要的列: {missing_cols}")
            return False
        
        # 检查数据质量
        null_counts = df[required_cols].isnull().sum()
        if null_counts.any():
            print(f"⚠️  发现缺失数据:")
            for col, count in null_counts.items():
                if count > 0:
                    print(f"    {col}: {count} ({count/len(df):.2%})")
        
        # 检查各证券的数据量
        code_counts = df['code'].value_counts()
        print(f"  各证券数据量: 最小={code_counts.min()}, 最大={code_counts.max()}, 平均={code_counts.mean():.0f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据检查失败: {str(e)}")
        return False


def quick_tokenizer_test(data_file: str, sample_size: int = 1000):
    """快速测试BarTokenizer"""
    print(f"\n=== 快速测试BarTokenizer ===")
    
    try:
        # 创建配置
        config = BarTokenizedDataset.get_default_config()
        config.block_size = 20  # 使用较小的block_size
        config.tokenizer.n_bins = 50  # 使用较小的词汇表
        config.balance.gini_threshold = 0.8  # 放宽阈值
        
        # 如果数据太大，先采样
        df = pd.read_parquet(data_file)
        if len(df) > sample_size:
            print(f"数据量较大，随机采样 {sample_size} 条记录进行测试")
            df = df.sample(n=sample_size, random_state=42).sort_values(['code', 'datetime'])
            
            # 保存临时文件
            temp_file = "temp_sample_data.parquet"
            df.to_parquet(temp_file, index=False)
            test_file = temp_file
        else:
            test_file = data_file
        
        # 创建数据集
        dataset = BarTokenizedDataset(config, test_file)
        
        print(f"✅ BarTokenizer测试成功")
        print(f"  样本数量: {len(dataset)}")
        print(f"  词汇表大小: {dataset.get_vocab_size()}")
        print(f"  代码数量: {dataset.get_code_size()}")
        
        # 获取分布统计
        stats = dataset.get_distribution_stats()
        print(f"  基尼系数: {stats.get('gini_coefficient', 'N/A'):.4f}")
        print(f"  标准化熵: {stats.get('normalized_entropy', 'N/A'):.4f}")
        
        # 测试数据加载
        if len(dataset) > 0:
            sample = dataset[0]
            print(f"  样本形状: code={sample[0].shape}, x={sample[1].shape}, x_mark={sample[2].shape}, y={sample[3].shape}")
        
        # 清理临时文件
        if test_file != data_file and os.path.exists(test_file):
            os.remove(test_file)
        
        return dataset
        
    except Exception as e:
        print(f"❌ BarTokenizer测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # 清理临时文件
        if 'temp_file' in locals() and os.path.exists(temp_file):
            os.remove(temp_file)
        
        return None


def quick_model_test(dataset):
    """快速测试模型"""
    print(f"\n=== 快速测试模型 ===")
    
    if dataset is None or len(dataset) == 0:
        print("❌ 数据集无效，跳过模型测试")
        return False
    
    try:
        # 创建小模型
        model = BarGpt4(
            block_size=dataset.config.block_size,
            code_size=dataset.get_code_size(),
            vocab_size=dataset.get_vocab_size(),
            n_layer=2,  # 小模型
            n_head=4,
            d_model=64,
            time_encoding='timeF',
            time_embed_type='time_feature',
            freq='t',
            pos_embed_type='rope',
            dropout=0.1
        )
        
        print(f"✅ 模型创建成功")
        print(f"  参数数量: {model.get_num_params():,}")
        
        # 测试前向传播
        code, x, x_mark, y = dataset[0]
        
        # 添加batch维度
        code = code.unsqueeze(0)
        x = x.unsqueeze(0)
        x_mark = x_mark.unsqueeze(0)
        y = y.unsqueeze(0)
        
        with torch.no_grad():
            logits, loss = model(code, x, x_mark, y)
        
        print(f"✅ 前向传播测试通过")
        print(f"  输出形状: {logits.shape}")
        print(f"  损失值: {loss.item():.4f}")
        
        # 测试预测多样性
        predictions = torch.argmax(logits, dim=-1)
        unique_preds = torch.unique(predictions).numel()
        
        print(f"  预测多样性: {unique_preds}/{dataset.get_vocab_size()} ({unique_preds/dataset.get_vocab_size():.2%})")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def quick_training_test(dataset, steps: int = 5):
    """快速训练测试"""
    print(f"\n=== 快速训练测试 ===")
    
    if dataset is None or len(dataset) == 0:
        print("❌ 数据集无效，跳过训练测试")
        return False
    
    try:
        from torch.utils.data import DataLoader
        
        # 创建数据加载器
        dataloader = DataLoader(dataset, batch_size=2, shuffle=True)
        
        # 创建模型
        model = BarGpt4(
            block_size=dataset.config.block_size,
            code_size=dataset.get_code_size(),
            vocab_size=dataset.get_vocab_size(),
            n_layer=2,
            n_head=4,
            d_model=64,
            time_encoding='timeF',
            time_embed_type='time_feature',
            freq='t',
            pos_embed_type='rope',
            dropout=0.1
        )
        
        # 创建优化器
        optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3)
        
        print(f"✅ 训练组件创建成功")
        
        # 执行训练步骤
        model.train()
        losses = []
        
        for step, batch in enumerate(dataloader):
            if step >= steps:
                break
            
            code, x, x_mark, y = batch
            
            # 前向传播
            logits, loss = model(code, x, x_mark, y)
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            losses.append(loss.item())
            print(f"  步骤 {step + 1}: 损失 = {loss.item():.4f}")
        
        print(f"✅ 训练测试通过")
        print(f"  平均损失: {np.mean(losses):.4f}")
        
        if len(losses) > 1:
            print(f"  损失变化: {losses[0]:.4f} -> {losses[-1]:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    parser = ArgumentParser(description='快速测试真实数据的BarTokenizer训练')
    parser.add_argument('--data_file', type=str, required=True, help='数据文件路径')
    parser.add_argument('--sample_size', type=int, default=2000, help='测试样本大小')
    parser.add_argument('--training_steps', type=int, default=5, help='训练测试步数')
    
    args = parser.parse_args()
    
    print("=== 快速测试真实数据的BarTokenizer训练 ===\n")
    
    # 1. 检查数据文件
    data_ok = quick_data_check(args.data_file)
    if not data_ok:
        print("\n❌ 数据检查失败，请修复数据问题后重试")
        return
    
    # 2. 测试BarTokenizer
    dataset = quick_tokenizer_test(args.data_file, args.sample_size)
    if dataset is None:
        print("\n❌ BarTokenizer测试失败，请检查错误信息")
        return
    
    # 3. 测试模型
    model_ok = quick_model_test(dataset)
    if not model_ok:
        print("\n❌ 模型测试失败，请检查错误信息")
        return
    
    # 4. 测试训练
    training_ok = quick_training_test(dataset, args.training_steps)
    if not training_ok:
        print("\n❌ 训练测试失败，请检查错误信息")
        return
    
    # 总结
    print(f"\n=== 测试总结 ===")
    print(f"✅ 数据检查: 通过")
    print(f"✅ BarTokenizer: 通过")
    print(f"✅ 模型集成: 通过")
    print(f"✅ 训练流程: 通过")
    
    print(f"\n🎉 所有测试通过！可以开始正式训练。")
    print(f"\n建议的训练命令:")
    print(f"python train_bar_gpt4_with_tokenizer.py \\")
    print(f"  --data_file {args.data_file} \\")
    print(f"  --mapping_strategy quantile \\")
    print(f"  --balancing_strategy frequency \\")
    print(f"  --n_bins 100 \\")
    print(f"  --block_size 30 \\")
    print(f"  --batch_size 64 \\")
    print(f"  --max_epochs 8 \\")
    print(f"  --k_folds 3 \\")
    print(f"  --use_class_weights")


if __name__ == '__main__':
    main()
