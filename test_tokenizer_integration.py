"""
测试BarTokenizer与BarGpt4模型的集成

这个脚本用于验证整个流程是否正常工作，包括：
1. 数据加载和处理
2. BarTokenizer的token化
3. 数据集创建
4. 模型训练（小规模测试）
"""

import os
import sys
import torch
import numpy as np
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pyqlab.data.dataset.dataset_bar_tokenized import BarTokenizedDataset
from pyqlab.data.tokenizers.bar_tokenizer import BarTokenizer
from pyqlab.models.gpt.bar_gpt4 import BarGpt4


def create_sample_data(n_samples: int = 500, n_codes: int = 3) -> pd.DataFrame:
    """创建示例数据用于测试"""
    print(f"创建示例数据: {n_samples} 条记录, {n_codes} 个证券")
    
    np.random.seed(42)
    
    # 生成时间序列
    base_date = pd.Timestamp('2025-01-01 09:30:00')
    dates = pd.date_range(base_date, periods=n_samples, freq='1min')
    
    data_rows = []
    
    for code_idx in range(n_codes):
        code = f"TEST{code_idx:03d}"
        base_price = 100 + code_idx * 10
        
        # 生成价格序列（随机游走）
        price_changes = np.random.randn(n_samples) * 0.01
        prices = base_price * np.exp(np.cumsum(price_changes))
        
        for i, (date, price) in enumerate(zip(dates, prices)):
            # 生成OHLC数据
            noise = np.random.randn(4) * 0.005
            open_price = price * (1 + noise[0])
            close_price = price * (1 + noise[1])
            high_price = max(open_price, close_price) * (1 + abs(noise[2]))
            low_price = min(open_price, close_price) * (1 - abs(noise[3]))
            
            # 生成成交量
            volume = int(np.random.lognormal(8, 0.5))
            
            data_rows.append({
                'datetime': date,
                'code': code,
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': volume
            })
    
    df = pd.DataFrame(data_rows)
    df = df.sort_values(['code', 'datetime']).reset_index(drop=True)
    
    print(f"✅ 示例数据创建完成")
    print(f"  时间范围: {df['datetime'].min()} 到 {df['datetime'].max()}")
    print(f"  证券代码: {df['code'].unique().tolist()}")
    
    return df


def test_bar_tokenizer(df: pd.DataFrame):
    """测试BarTokenizer功能"""
    print("\n=== 测试BarTokenizer ===")
    
    # 测试不同的映射策略
    strategies = ['linear', 'quantile', 'adaptive']
    results = {}
    
    for strategy in strategies:
        print(f"\n--- 测试 {strategy} 映射策略 ---")
        
        try:
            tokenizer = BarTokenizer(
                mapping_strategy=strategy,
                balancing_strategy='frequency',
                n_bins=50,
                features=['change', 'body', 'upper_shadow', 'lower_shadow', 'volume_ratio'],
                atr_period=14
            )
            
            # 使用第一个证券的数据进行测试
            test_code = df['code'].iloc[0]
            code_data = df[df['code'] == test_code].copy()
            
            if len(code_data) < 20:
                print(f"  跳过：数据量不足")
                continue
            
            # 拟合和转换
            tokens = tokenizer.fit_transform(code_data)
            
            # 分析结果
            balance_metrics = tokenizer.analyze_balance(tokens)
            
            results[strategy] = {
                'success': True,
                'vocab_size': tokenizer.get_vocab_size(),
                'n_tokens': len(tokens),
                'gini_coefficient': balance_metrics['gini_coefficient'],
                'normalized_entropy': balance_metrics['normalized_entropy']
            }
            
            print(f"  ✅ 成功")
            print(f"  词汇表大小: {tokenizer.get_vocab_size()}")
            print(f"  生成tokens: {len(tokens)}")
            print(f"  基尼系数: {balance_metrics['gini_coefficient']:.4f}")
            print(f"  标准化熵: {balance_metrics['normalized_entropy']:.4f}")
            
        except Exception as e:
            print(f"  ❌ 失败: {str(e)}")
            results[strategy] = {'success': False, 'error': str(e)}
    
    return results


def test_dataset_creation(df: pd.DataFrame):
    """测试数据集创建"""
    print("\n=== 测试数据集创建 ===")
    
    try:
        # 保存临时数据文件
        temp_file = "temp_test_data.parquet"
        df.to_parquet(temp_file, index=False)
        
        # 创建配置
        config = BarTokenizedDataset.get_default_config()
        config.block_size = 10  # 使用较小的block_size进行测试
        config.tokenizer.n_bins = 30
        config.balance.gini_threshold = 0.8  # 放宽阈值
        
        # 创建数据集
        dataset = BarTokenizedDataset(config, temp_file)
        
        print(f"✅ 数据集创建成功")
        print(f"  样本数量: {len(dataset)}")
        print(f"  词汇表大小: {dataset.get_vocab_size()}")
        print(f"  代码数量: {dataset.get_code_size()}")
        
        # 测试数据加载
        if len(dataset) > 0:
            sample = dataset[0]
            print(f"  样本形状: code={sample[0].shape}, x={sample[1].shape}, x_mark={sample[2].shape}, y={sample[3].shape}")
            
            # 测试多个样本
            for i in range(min(3, len(dataset))):
                code, x, x_mark, y = dataset[i]
                assert x.shape[0] == config.block_size, f"序列长度不匹配: {x.shape[0]} != {config.block_size}"
                assert len(x) == len(y), f"输入输出长度不匹配: {len(x)} != {len(y)}"
            
            print(f"  ✅ 数据加载测试通过")
        
        # 清理临时文件
        if os.path.exists(temp_file):
            os.remove(temp_file)
        
        return dataset
        
    except Exception as e:
        print(f"❌ 数据集创建失败: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # 清理临时文件
        if os.path.exists(temp_file):
            os.remove(temp_file)
        
        return None


def test_model_integration(dataset):
    """测试模型集成"""
    print("\n=== 测试模型集成 ===")
    
    if dataset is None or len(dataset) == 0:
        print("❌ 数据集无效，跳过模型测试")
        return
    
    try:
        # 创建模型
        model = BarGpt4(
            block_size=dataset.config.block_size,
            code_size=dataset.get_code_size(),
            vocab_size=dataset.get_vocab_size(),
            n_layer=2,  # 使用较小的模型进行测试
            n_head=4,
            d_model=64,
            time_encoding='timeF',
            time_embed_type='time_feature',
            freq='t',
            pos_embed_type='rope',
            dropout=0.1
        )
        
        print(f"✅ 模型创建成功")
        print(f"  参数数量: {model.get_num_params():,}")
        
        # 测试前向传播
        code, x, x_mark, y = dataset[0]
        
        # 添加batch维度
        code = code.unsqueeze(0)
        x = x.unsqueeze(0)
        x_mark = x_mark.unsqueeze(0)
        y = y.unsqueeze(0)
        
        # 前向传播
        with torch.no_grad():
            logits, loss = model(code, x, x_mark, y)
        
        print(f"✅ 前向传播测试通过")
        print(f"  输出形状: {logits.shape}")
        print(f"  损失值: {loss.item():.4f}")
        
        # 测试预测多样性
        predictions = torch.argmax(logits, dim=-1)
        unique_preds = torch.unique(predictions).numel()
        total_preds = predictions.numel()
        
        print(f"  预测多样性: {unique_preds}/{dataset.get_vocab_size()} ({unique_preds/dataset.get_vocab_size():.2%})")
        
        # 测试多个batch
        batch_size = min(4, len(dataset))
        batch_data = [dataset[i] for i in range(batch_size)]
        
        # 组合batch
        batch_code = torch.stack([item[0] for item in batch_data])
        batch_x = torch.stack([item[1] for item in batch_data])
        batch_x_mark = torch.stack([item[2] for item in batch_data])
        batch_y = torch.stack([item[3] for item in batch_data])
        
        with torch.no_grad():
            batch_logits, batch_loss = model(batch_code, batch_x, batch_x_mark, batch_y)
        
        print(f"✅ 批处理测试通过")
        print(f"  批次大小: {batch_size}")
        print(f"  批次输出形状: {batch_logits.shape}")
        print(f"  批次损失: {batch_loss.item():.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_training_step(dataset):
    """测试训练步骤"""
    print("\n=== 测试训练步骤 ===")
    
    if dataset is None or len(dataset) == 0:
        print("❌ 数据集无效，跳过训练测试")
        return
    
    try:
        from torch.utils.data import DataLoader
        
        # 创建数据加载器
        dataloader = DataLoader(dataset, batch_size=2, shuffle=True)
        
        # 创建模型
        model = BarGpt4(
            block_size=dataset.config.block_size,
            code_size=dataset.get_code_size(),
            vocab_size=dataset.get_vocab_size(),
            n_layer=2,
            n_head=4,
            d_model=64,
            time_encoding='timeF',
            time_embed_type='time_feature',
            freq='t',
            pos_embed_type='rope',
            dropout=0.1
        )
        
        # 创建优化器
        optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3)
        
        print(f"✅ 训练组件创建成功")
        
        # 执行几个训练步骤
        model.train()
        losses = []
        
        for step, batch in enumerate(dataloader):
            if step >= 3:  # 只测试3个步骤
                break
            
            code, x, x_mark, y = batch
            
            # 前向传播
            logits, loss = model(code, x, x_mark, y)
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            losses.append(loss.item())
            print(f"  步骤 {step + 1}: 损失 = {loss.item():.4f}")
        
        print(f"✅ 训练步骤测试通过")
        print(f"  平均损失: {np.mean(losses):.4f}")
        print(f"  损失变化: {losses[0]:.4f} -> {losses[-1]:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练步骤测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("=== BarTokenizer与BarGpt4集成测试 ===\n")
    
    # 1. 创建示例数据
    df = create_sample_data(n_samples=200, n_codes=2)
    
    # 2. 测试BarTokenizer
    tokenizer_results = test_bar_tokenizer(df)
    
    # 3. 测试数据集创建
    dataset = test_dataset_creation(df)
    
    # 4. 测试模型集成
    model_success = test_model_integration(dataset)
    
    # 5. 测试训练步骤
    training_success = test_training_step(dataset)
    
    # 总结测试结果
    print(f"\n=== 测试总结 ===")
    
    print(f"BarTokenizer测试:")
    for strategy, result in tokenizer_results.items():
        status = "✅" if result.get('success', False) else "❌"
        print(f"  {strategy}: {status}")
    
    print(f"数据集创建: {'✅' if dataset is not None else '❌'}")
    print(f"模型集成: {'✅' if model_success else '❌'}")
    print(f"训练步骤: {'✅' if training_success else '❌'}")
    
    # 检查整体状态
    all_success = (
        any(r.get('success', False) for r in tokenizer_results.values()) and
        dataset is not None and
        model_success and
        training_success
    )
    
    if all_success:
        print(f"\n🎉 所有测试通过！可以开始正式训练。")
        print(f"\n建议的下一步:")
        print(f"1. 准备真实的min1数据: python prepare_min1_data.py")
        print(f"2. 开始训练: python train_bar_gpt4_with_tokenizer.py --data_file your_data.parquet")
    else:
        print(f"\n⚠️  部分测试失败，请检查错误信息并修复问题。")


if __name__ == '__main__':
    main()
